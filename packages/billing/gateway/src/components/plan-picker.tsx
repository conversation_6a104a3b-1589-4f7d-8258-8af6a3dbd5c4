'use client';

import { useMemo } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  BillingConfig,
  type LineItemSchema,
  getPlanIntervals,
  getPrimaryLineItem,
  getProductPlanPair,
} from '@kit/billing';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import {
  RadioGroup,
  RadioGroupItem,
} from '@kit/ui/radio-group';
import { Separator } from '@kit/ui/separator';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { LineItemDetails } from './line-item-details';
import { PlanCostDisplay } from './plan-cost-display';

export function PlanPicker(
  props: React.PropsWithChildren<{
    config: BillingConfig;
    onSubmit: (data: { planId: string; productId: string }) => void;
    canStartTrial?: boolean;
    pending?: boolean;
  }>,
) {
  const { t } = useTranslation(`billing`);

  const intervals = useMemo(
    () => getPlanIntervals(props.config),
    [props.config],
  ) as string[];

  const form = useForm({
    reValidateMode: 'onChange',
    mode: 'onChange',
    resolver: zodResolver(
      z
        .object({
          planId: z.string(),
          productId: z.string(),
          interval: z.string().optional(),
        })
        .refine(
          (data) => {
            try {
              const { product, plan } = getProductPlanPair(
                props.config,
                data.planId,
              );

              return product && plan;
            } catch {
              return false;
            }
          },
          { message: t('noPlanChosen'), path: ['planId'] },
        ),
    ),
    defaultValues: {
      interval: intervals[0],
      planId: '',
      productId: '',
    },
  });

  const selectedInterval = useWatch({
    name: 'interval',
    control: form.control,
  });

  const planId = form.getValues('planId');

  const { plan: selectedPlan, product: selectedProduct } = useMemo(() => {
    try {
      return getProductPlanPair(props.config, planId);
    } catch {
      return {
        plan: null,
        product: null,
      };
    }
  }, [props.config, planId]);

  // display the period picker if the selected plan is recurring or if no plan is selected
  const isRecurringPlan =
    selectedPlan?.paymentType === 'recurring' || !selectedPlan;

  // Always filter out hidden products
  const visibleProducts = props.config.products.filter(
    (product) => !product.hidden,
  );

  return (
    <Form {...form}>
      <form
        className={'flex w-full flex-col gap-y-8'}
        onSubmit={form.handleSubmit(props.onSubmit)}
      >
        <div className={'flex flex-col gap-y-8'}>
          {/* Header Section */}
          <div className={'text-center max-w-3xl mx-auto'}>
            <div className={'inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800 mb-6'}>
              Pricing
            </div>
            <h2 className={'text-4xl font-bold tracking-tight mb-4'}>
              Prices that make sense!
            </h2>
            <p className={'text-muted-foreground text-lg max-w-2xl mx-auto'}>
              Managing a small business today is already tough.
            </p>
          </div>

          <If condition={intervals.length}>
            <div
              className={cn('transition-all', {
                ['pointer-events-none opacity-50']: !isRecurringPlan,
              })}
            >
              <FormField
                name={'interval'}
                render={({ field }) => {
                  return (
                    <FormItem className={'flex flex-col gap-4'}>
                      <FormLabel htmlFor={'plan-picker-id'} className={'text-center'}>
                        <Trans i18nKey={'common:billingInterval.label'} />
                      </FormLabel>

                      <FormControl id={'plan-picker-id'}>
                        <RadioGroup name={field.name} value={field.value}>
                          <div className={'flex justify-center space-x-2.5'}>
                            {intervals.map((interval) => {
                              const selected = field.value === interval;

                              return (
                                <label
                                  htmlFor={interval}
                                  key={interval}
                                  className={cn(
                                    'focus-within:border-primary flex items-center gap-x-2.5 rounded-md border px-2.5 py-2 transition-colors',
                                    {
                                      ['bg-muted border-input']: selected,
                                      ['hover:border-input border-transparent']:
                                        !selected,
                                    },
                                  )}
                                >
                                  <RadioGroupItem
                                    id={interval}
                                    value={interval}
                                    onClick={() => {
                                      form.setValue('interval', interval, {
                                        shouldValidate: true,
                                      });

                                      if (selectedProduct) {
                                        const plan = selectedProduct.plans.find(
                                          (item) => item.interval === interval,
                                        );

                                        form.setValue(
                                          'planId',
                                          plan?.id ?? '',
                                          {
                                            shouldValidate: true,
                                            shouldDirty: true,
                                            shouldTouch: true,
                                          },
                                        );
                                      }
                                    }}
                                  />

                                  <span
                                    className={cn('text-sm', {
                                      ['cursor-pointer']: !selected,
                                    })}
                                  >
                                    <Trans
                                      i18nKey={`billing:billingInterval.${interval}`}
                                    />
                                  </span>
                                </label>
                              );
                            })}
                          </div>
                        </RadioGroup>
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
          </If>

          {/* Plans Grid */}
          <FormField
            name={'planId'}
            render={({ field }) => (
              <FormItem className={'flex flex-col gap-6'}>
                <FormControl>
                  <RadioGroup value={field.value} name={field.name}>
                    <div className={'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto'}>
                      {visibleProducts.map((product) => {
                        const plan = product.plans.find((item) => {
                          if (item.paymentType === 'one-time') {
                            return true;
                          }

                          return item.interval === selectedInterval;
                        });

                        if (!plan || plan.custom) {
                          return null;
                        }

                        const planId = plan.id;
                        const selected = field.value === planId;

                        const primaryLineItem = getPrimaryLineItem(
                          props.config,
                          planId,
                        );

                        if (!primaryLineItem) {
                          throw new Error(`Base line item was not found`);
                        }

                        return (
                          <Card
                            key={primaryLineItem.id}
                            className={cn(
                              'relative cursor-pointer transition-all duration-200 hover:shadow-lg',
                              {
                                'ring-2 ring-primary shadow-lg': selected,
                                'hover:border-gray-300': !selected,
                              }
                            )}
                            onClick={() => {
                              if (selected) {
                                return;
                              }

                              form.setValue('planId', planId, {
                                shouldValidate: true,
                              });

                              form.setValue('productId', product.id, {
                                shouldValidate: true,
                              });
                            }}
                          >
                            <RadioGroupItem
                              data-test-plan={plan.id}
                              id={plan.id}
                              value={plan.id}
                              className={'absolute top-4 right-4'}
                            />

                            <CardHeader className={'pb-4'}>
                              <div className={'flex flex-col gap-2'}>
                                <h3 className={'text-xl font-semibold'}>
                                  <Trans
                                    i18nKey={`billing:plans.${product.id}.name`}
                                    defaults={product.name}
                                  />
                                </h3>
                                <p className={'text-muted-foreground text-sm'}>
                                  <Trans
                                    i18nKey={`billing:plans.${product.id}.description`}
                                    defaults={product.description}
                                  />
                                </p>
                              </div>
                            </CardHeader>

                            <CardContent className={'pb-6'}>
                              <div className={'mb-6'}>
                                <div className={'text-4xl font-bold mb-1'}>
                                  <Price key={plan.id}>
                                    <PlanCostDisplay
                                      primaryLineItem={primaryLineItem}
                                      currencyCode={product.currency}
                                      interval={selectedInterval}
                                      alwaysDisplayMonthlyPrice={true}
                                    />
                                  </Price>
                                </div>
                                <div className={'text-muted-foreground text-sm'}>
                                  <If
                                    condition={
                                      plan.paymentType === 'recurring'
                                    }
                                    fallback={
                                      <Trans i18nKey={`billing:lifetime`} />
                                    }
                                  >
                                    / month
                                  </If>
                                </div>
                              </div>

                              {/* Features List */}
                              <div className={'space-y-3'}>
                                {product.features.slice(0, 3).map((feature) => (
                                  <div key={feature} className={'flex items-start gap-3'}>
                                    <CheckCircle className={'h-5 w-5 text-green-500 flex-shrink-0 mt-0.5'} />
                                    <span className={'text-sm text-gray-700'}>
                                      <Trans i18nKey={feature} defaults={'Fast and reliable'} />
                                    </span>
                                  </div>
                                ))}
                                {product.features.length === 0 && (
                                  <>
                                    <div className={'flex items-start gap-3'}>
                                      <CheckCircle className={'h-5 w-5 text-green-500 flex-shrink-0 mt-0.5'} />
                                      <span className={'text-sm text-gray-700'}>
                                        Fast and reliable
                                      </span>
                                    </div>
                                    <div className={'flex items-start gap-3'}>
                                      <CheckCircle className={'h-5 w-5 text-green-500 flex-shrink-0 mt-0.5'} />
                                      <span className={'text-sm text-gray-700'}>
                                        We&apos;ve made it fast and reliable.
                                      </span>
                                    </div>
                                    <div className={'flex items-start gap-3'}>
                                      <CheckCircle className={'h-5 w-5 text-green-500 flex-shrink-0 mt-0.5'} />
                                      <span className={'text-sm text-gray-700'}>
                                        Fast and reliable
                                      </span>
                                    </div>
                                  </>
                                )}
                              </div>
                            </CardContent>

                            <CardFooter className={'pt-0'}>
                              <Button
                                type="button"
                                variant={selected ? 'default' : 'outline'}
                                className={cn('w-full', {
                                  'bg-black text-white hover:bg-gray-800': selected,
                                  'border-gray-300 text-gray-700 hover:bg-gray-50': !selected,
                                })}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (selected) {
                                    return;
                                  }

                                  form.setValue('planId', planId, {
                                    shouldValidate: true,
                                  });

                                  form.setValue('productId', product.id, {
                                    shouldValidate: true,
                                  });
                                }}
                              >
                                <If
                                  condition={plan.trialDays && props.canStartTrial}
                                  fallback={selected ? 'Selected' : 'Sign up today'}
                                >
                                  Start Free Trial
                                </If>
                                <ArrowRight className={'ml-2 h-4 w-4'} />
                              </Button>

                              <If condition={plan.trialDays && props.canStartTrial}>
                                <Badge
                                  className={'absolute -top-2 left-1/2 transform -translate-x-1/2 px-2 py-1 text-xs'}
                                  variant={'success'}
                                >
                                  <Trans
                                    i18nKey={`billing:trialPeriod`}
                                    values={{
                                      period: plan.trialDays,
                                    }}
                                  />
                                </Badge>
                              </If>
                            </CardFooter>
                          </Card>
                        );
                      })}
                    </div>
                  </RadioGroup>
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <div className={'flex justify-center'}>
            <Button
              data-test="checkout-submit-button"
              disabled={props.pending ?? !form.formState.isValid}
              size="lg"
              className={'px-8'}
            >
              {props.pending ? (
                t('redirectingToPayment')
              ) : (
                <>
                  <If
                    condition={selectedPlan?.trialDays && props.canStartTrial}
                    fallback={t(`proceedToPayment`)}
                  >
                    <span>{t(`startTrial`)}</span>
                  </If>

                  <ArrowRight className={'ml-2 h-4 w-4'} />
                </>
              )}
            </Button>
          </div>
        </div>
      </form>

      {/* Plan Details Sidebar */}
      {selectedPlan && selectedInterval && selectedProduct ? (
        <div className={'mt-8'}>
          <PlanDetails
            selectedInterval={selectedInterval}
            selectedPlan={selectedPlan}
            selectedProduct={selectedProduct}
          />
        </div>
      ) : null}
    </Form>
  );
}

function PlanDetails({
  selectedProduct,
  selectedInterval,
  selectedPlan,
}: {
  selectedProduct: {
    id: string;
    name: string;
    description: string;
    currency: string;
    features: string[];
  };

  selectedInterval: string;

  selectedPlan: {
    lineItems: z.infer<typeof LineItemSchema>[];
    paymentType: string;
  };
}) {
  const isRecurring = selectedPlan.paymentType === 'recurring';

  // trick to force animation on re-render
  const key = Math.random();

  return (
    <div
      key={key}
      className={
        'fade-in animate-in zoom-in-95 flex w-full flex-col space-y-4 py-2 lg:px-8'
      }
    >
      <div className={'flex flex-col space-y-0.5'}>
        <span className={'text-sm font-medium'}>
          <b>
            <Trans
              i18nKey={`billing:plans.${selectedProduct.id}.name`}
              defaults={selectedProduct.name}
            />
          </b>{' '}
          <If condition={isRecurring}>
            / <Trans i18nKey={`billing:billingInterval.${selectedInterval}`} />
          </If>
        </span>

        <p>
          <span className={'text-muted-foreground text-sm'}>
            <Trans
              i18nKey={`billing:plans.${selectedProduct.id}.description`}
              defaults={selectedProduct.description}
            />
          </span>
        </p>
      </div>

      <If condition={selectedPlan.lineItems.length > 0}>
        <Separator />

        <div className={'flex flex-col space-y-2'}>
          <span className={'text-sm font-semibold'}>
            <Trans i18nKey={'billing:detailsLabel'} />
          </span>

          <LineItemDetails
            lineItems={selectedPlan.lineItems ?? []}
            selectedInterval={isRecurring ? selectedInterval : undefined}
            currency={selectedProduct.currency}
          />
        </div>
      </If>

      <Separator />

      <div className={'flex flex-col space-y-2'}>
        <span className={'text-sm font-semibold'}>
          <Trans i18nKey={'billing:featuresLabel'} />
        </span>

        {selectedProduct.features.map((item) => {
          return (
            <div key={item} className={'flex items-center gap-x-2 text-sm'}>
              <CheckCircle className={'h-4 text-green-500'} />

              <span className={'text-secondary-foreground'}>
                <Trans i18nKey={item} defaults={item} />
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

function Price(props: React.PropsWithChildren) {
  return (
    <span
      className={
        'animate-in slide-in-from-left-4 fade-in font-bold tracking-tight duration-500'
      }
    >
      {props.children}
    </span>
  );
}
