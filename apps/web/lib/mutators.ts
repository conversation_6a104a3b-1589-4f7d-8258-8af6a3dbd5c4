import {CustomMutatorDefs} from '@rocicorp/zero';
import {schema} from '../app/types/schema'

interface ICPInsert {
  company_id: string;
  withAi?: boolean;
  withLinkedIn?: boolean;
  name: string | null;
  data: any;
  error_generating: boolean;
  is_generating?: boolean;
  reference_products?: string[];
  reference_description?: string;
  linkedInUrls?: string[];
}

// Generate a simple unique ID (similar to getUniqueId from utils.ts)
function generateUniqueId(length = 10): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Generate slug from campaign name (similar to campaign.ts)
function generateSlug(name: string): string {
  return `${name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-') // Replace any non-alphanumeric char with hyphen
    .replace(/^-+|-+$/g, '')}-${generateUniqueId()}`; // Remove leading/trailing hyphens
}

export function createMutators() {
    return {
      accounts: {
        insert: async (tx, { id, name, primary_owner_user_id, is_personal_account, website }: {
          id: string;
          name: string;
          primary_owner_user_id: string;
          is_personal_account: boolean;
          website: string;
        }) => {

          await tx.mutate.accounts.insert({
            id,
            name,
            primary_owner_user_id,
            is_personal_account,
            website,
            public_data: {},
          });
        },
      },
      company_brand: {
        insert: async (tx, { 
          id, 
          values,
          isHTML,
          html,
          brand_document_blob_obj
        }: {
          id: string;
          values: any;
          isHTML: boolean;
          html: string | null;
          brand_document_blob_obj: {
            url: string;
            pathname: string;
            downloadUrl: string;
          } | null;
        }) => {
          await tx.mutate.company_brand.insert({
            id,
            ...values,
          });
        },
        update: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.company_brand.update({
            id,
            ...values,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.company_brand.delete({
            id,
          });
        },
      },
      post_engagement_details: {
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.post_engagement_details.insert({
            id,
            ...values,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.post_engagement_details.delete({
            id,
          });
        },
      },
      site_research: {
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;  
        }) => {
          await tx.mutate.site_research.insert({
            id,
            ...values,
          });
        },
      },
      ayrshare_user_profile: {
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.ayrshare_user_profile.insert({
            id,
            ...values,
          });
        },
        update: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.ayrshare_user_profile.update({
            id,
            ...values,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.ayrshare_user_profile.delete({
            id,
          });
        },
      },
      ayrshare_social_profiles: {
        upsert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.ayrshare_social_profiles.upsert({
            id,
            ...values,
          });
        },
      },
      generated_research: {
        insert: async (tx, { 
          id, 
          account_id, 
          icp_id, 
          persona_id, 
          research_type, 
          time_filter, 
          title, 
          topic,
          created_by 
        }: {
          id: string;
          account_id: string;
          icp_id: string;
          persona_id: string | null;
          research_type: string;
          time_filter: string;
          title: string;
          topic?: string;
          created_by: string;
        }) => {
          const now = Date.now();
          
          await tx.mutate.generated_research.insert({
            id,
            account_id,
            icp_id,
            persona_id,
            research_type,
            time_filter,
            title,
            topic,
            results: [],
            content_suggestions: [],
            is_generating: true,
            created_at: now,
            updated_at: now,
            created_by,
            updated_by: created_by,
          });
        },
      },
      saved_research: {
        insert: async (tx, { 
          id, 
          account_id, 
          icp_id, 
          persona_id, 
          research_type, 
          time_filter, 
          title, 
          topic,
          description,
          source,
          source_url
        }: {
          id: string;
          account_id: string;
          icp_id: string;
          persona_id: string | null;
          research_type: string;
          time_filter: string;
          title: string;
          topic?: string;
          description: string;
          source: string;
          source_url: string;
        }) => {
          console.log({persona_id})
          await tx.mutate.saved_research.insert({
            id,
            account_id,
            icp_id,
            persona_id,
            research_type,
            time_filter,
            title,
            topic,
            description,
            source,
            source_url
          });
        },
        update: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => { 
          await tx.mutate.saved_research.update({
            id,
            ...values,
          });
        },
      },
      company_campaigns: {
        insert: async (tx, { 
          id, 
          company_id, 
          user_id, 
          name, 
          objective, 
          start_date, 
          end_date, 
          templateId,
          external_research,
          products,
          target_icps,
          target_personas
        }: {
          id: string;
          company_id: string;
          user_id: string;
          name: string;
          objective: string;
          start_date: string;
          end_date: string;
          templateId: string;
          external_research?: string[];
          products?: string[];
          target_icps?: string[];
          target_personas?: string[];
        }) => {
          const now = Date.now();
          
          // Convert dates to timestamps
          const startTimestamp = new Date(start_date).getTime();
          const endTimestamp = new Date(end_date).getTime();
          
          await tx.mutate.company_campaigns.insert({
            id,
            created_at: now,
            company_id,
            user_id,
            name,
            slug: generateSlug(name),
            objective,
            start_date: startTimestamp,
            end_date: endTimestamp,
            is_generating: true,
            status: 'Draft',
            external_research: (external_research && external_research.length > 0) ? external_research : null,
            products: (products && products.length > 0) ? products : null,
            metadata: { templateId },
            target_icps: (target_icps && target_icps.length > 0) ? target_icps : null,
            target_personas: (target_personas && target_personas.length > 0) ? target_personas : null,
          });
        },
        update: async (tx, { 
          id,
          values,
          regenerate = false
        }: {
          id: string;
          values: any;
          regenerate?: boolean;
        }) => {
            await tx.mutate.company_campaigns.update({
              id,
            ...values,
          });
        },
      },
      user_cache: {
        upsert: async (tx, { 
          user_id, 
          values
        }: {
          user_id: string;
          values: any;
        }) => {
          const now = Date.now();
          
          await tx.mutate.user_cache.upsert({
            user_id,
            ...values,
            created_at: now,
          });
        },
        update: async (tx, { 
          user_id, 
          values
        }: {
          user_id: string;
          values: any;
        }) => {
          await tx.mutate.user_cache.update({
            user_id,
            ...values,
          });
        },
      },
      icps: {
        update: async (tx, { 
          id, 
          name,
          data
        }: {
          id: string;
          name: string;
          data: any;
        }) => {
          await tx.mutate.icps.update({
            id,
            name,
            data,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.icps.delete({
            id,
          });
        },
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values : ICPInsert;
        }) => {
          const now = Date.now();
          await tx.mutate.icps.insert({
            id,
            ...values,
            created_at: now,
          });
        },
      },
      personas: {
        update: async (tx, { 
          id, 
          name,
          data
        }: {
          id: string;
          name: string;
          data: any;
        }) => {
          await tx.mutate.personas.update({
            id,
            name,
            data,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.personas.delete({
            id,
          });
        },
        insert: async (tx, { 
          id, 
          company_id,
          icp_id,
          withAi,
          name,
          data = {},
          error_generating = false
        }: {
          id: string;
          company_id: string;
          icp_id: string;
          withAi: boolean;
          name: string | null;
          data: any;
          error_generating: boolean;
        }) => {
          const now = Date.now();
          await tx.mutate.personas.insert({
            id,
            company_id,
            icp_id,
            created_at: now,
            is_generating: withAi ? true : false,
            name,
            data,
            error_generating,
          });
        },
      },
      company_content: {
        update: async (tx, args: {
          id: string;
          values?: any;
          [key: string]: any;
        }) => {
          // Extract values if provided, otherwise use all args except id
          const { id, values, ...directValues } = args;
          const updateData = values || directValues;
          const now = Date.now();

          // Automatically set updated_at timestamp for any update
          const updatedValues = {
            ...updateData,
            updated_at: now,
          };


          await tx.mutate.company_content.update({
            id,
            ...updatedValues,
          });
        },
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.company_content.insert({
            id,
            ...values,
          });
        },
      },
      company_task_statuses: {
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.company_task_statuses.insert({
            id,
            ...values,
          });
        },
        update: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.company_task_statuses.update({
            id,
            ...values,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.company_task_statuses.delete({
            id,
          });
        },
      },
      socials_research: {
        insert: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          await tx.mutate.socials_research.insert({
            id,
            ...values,
          });
        },
      },
      products: {
        insert: async (tx, { 
          id, 
          name,
          description,
          target_audience,
          key_features,
          company_id,
          custom_fields
        }: {
          id: string;
          name: string;
          description?: string;
          target_audience?: string[];
          key_features?: Array<{name: string; value_prop: string; differentiation: string}>;
          company_id: string;
          custom_fields?: any;
        }) => {
          const now = Date.now();
          await tx.mutate.products.insert({
            id,
            name,
            description,
            target_audience: target_audience || [],
            key_features: key_features || [],
            company_id,
            custom_fields: custom_fields || {},
            created_at: now,
            updated_at: now,
          });
        },
        update: async (tx, { 
          id, 
          values
        }: {
          id: string;
          values: any;
        }) => {
          const now = Date.now();
          await tx.mutate.products.update({
            id,
            ...values,
            updated_at: now,
          });
        },
        delete: async (tx, { 
          id, 
        }: {
          id: string;
        }) => {
          await tx.mutate.products.delete({
            id,
          });
        },
      },
    }
  }